use crate::{manifest::Manifest, registry::RegistryClient, ui::UI, NxError, Result};
use semver::{Version, VersionReq};
use std::collections::{HashMap, HashSet, VecDeque};
use rayon::prelude::*;

/// Dependency resolver
pub struct Resolver {
    registry: RegistryClient,
    ui: UI,
}

/// Resolved dependency
#[derive(Debug, Clone)]
pub struct ResolvedDependency {
    pub name: String,
    pub version: String,
    pub tarball_url: String,
    pub integrity: Option<String>,
    pub dependencies: HashMap<String, String>,
}

/// Resolution result
#[derive(Debug)]
pub struct ResolutionResult {
    pub dependencies: Vec<ResolvedDependency>,
    pub conflicts: Vec<DependencyConflict>,
}

/// Dependency conflict
#[derive(Debug)]
pub struct DependencyConflict {
    pub package: String,
    pub requested_versions: Vec<String>,
    pub resolved_version: String,
}

impl Resolver {
    /// Create a new resolver
    pub fn new(registry: RegistryClient, ui: UI) -> Self {
        Self { registry, ui }
    }

    /// Resolve dependencies from a manifest
    pub async fn resolve_from_manifest(&self, manifest: &Manifest) -> Result<ResolutionResult> {
        let dependencies = manifest.get_all_dependencies();
        self.resolve_dependencies(dependencies).await
    }

    /// Resolve a set of dependencies
    pub async fn resolve_dependencies(
        &self,
        dependencies: HashMap<String, String>,
    ) -> Result<ResolutionResult> {
        let mut resolved: HashMap<String, ResolvedDependency> = HashMap::new();
        let mut conflicts = Vec::new();
        let mut queue = VecDeque::new();
        let mut visited = HashSet::new();

        // Initialize queue with root dependencies
        for (name, version_req) in dependencies {
            queue.push_back((name, version_req, 0)); // (name, version_req, depth)
        }

        let total_estimate = queue.len() as u64 * 3; // Rough estimate
        let pb = self.ui.resolution_progress(total_estimate);
        let mut processed = 0;

        while let Some((package_name, version_req, depth)) = queue.pop_front() {
            let key = format!("{}@{}", package_name, version_req);
            if visited.contains(&key) {
                continue;
            }
            visited.insert(key);

            pb.set_message(format!("Resolving {}", package_name));
            pb.set_position(processed);
            processed += 1;

            // Fetch package metadata
            let metadata = match self.registry.get_package_metadata(&package_name).await {
                Ok(metadata) => metadata,
                Err(NxError::PackageNotFound(_)) => {
                    self.ui.warning(&format!("Package '{}' not found, skipping", package_name));
                    continue;
                }
                Err(e) => return Err(e),
            };

            // Resolve version
            let resolved_version = self.registry.resolve_version(&metadata, &version_req)?;
            
            // Check for conflicts
            if let Some(existing) = resolved.get(&package_name) {
                if existing.version != resolved_version {
                    conflicts.push(DependencyConflict {
                        package: package_name.clone(),
                        requested_versions: vec![existing.version.clone(), resolved_version.clone()],
                        resolved_version: self.resolve_conflict(&existing.version, &resolved_version)?,
                    });
                    continue;
                }
            }

            // Get version metadata
            let version_metadata = self.registry.get_version_metadata(&metadata, &resolved_version)?;

            // Add to resolved dependencies
            let resolved_dep = ResolvedDependency {
                name: package_name.clone(),
                version: resolved_version.clone(),
                tarball_url: version_metadata.dist.tarball.clone(),
                integrity: version_metadata.dist.integrity.clone(),
                dependencies: version_metadata.get_dependencies(),
            };

            resolved.insert(package_name.clone(), resolved_dep);

            // Add child dependencies to queue (limit depth to prevent infinite recursion)
            if depth < 10 {
                let deps = version_metadata.get_dependencies();
                for (dep_name, dep_version) in deps {
                    queue.push_back((dep_name.clone(), dep_version.clone(), depth + 1));
                }
            }
        }

        pb.finish_with_message("Dependencies resolved");

        Ok(ResolutionResult {
            dependencies: resolved.into_values().collect(),
            conflicts,
        })
    }

    /// Resolve version conflicts using semver precedence
    fn resolve_conflict(&self, version1: &str, version2: &str) -> Result<String> {
        let v1 = Version::parse(version1)?;
        let v2 = Version::parse(version2)?;

        // Choose the higher version
        if v1 > v2 {
            Ok(version1.to_string())
        } else {
            Ok(version2.to_string())
        }
    }

    /// Check if a version satisfies a requirement
    pub fn satisfies_requirement(version: &str, requirement: &str) -> Result<bool> {
        let version = Version::parse(version)?;
        let req = VersionReq::parse(requirement)?;
        Ok(req.matches(&version))
    }

    /// Find the best version for a requirement from available versions
    pub fn find_best_version(
        requirement: &str,
        available_versions: &[String],
    ) -> Result<Option<String>> {
        let req = VersionReq::parse(requirement)?;
        
        let mut matching_versions: Vec<_> = available_versions
            .iter()
            .filter_map(|v| Version::parse(v).ok())
            .filter(|v| req.matches(v))
            .collect();

        if matching_versions.is_empty() {
            return Ok(None);
        }

        // Sort by version (highest first)
        matching_versions.sort_by(|a, b| b.cmp(a));
        Ok(Some(matching_versions[0].to_string()))
    }

    /// Validate dependency tree for circular dependencies
    pub fn validate_dependency_tree(dependencies: &[ResolvedDependency]) -> Result<()> {
        let mut graph: HashMap<String, Vec<String>> = HashMap::new();
        
        // Build dependency graph
        for dep in dependencies {
            let deps: Vec<String> = dep.dependencies.keys().cloned().collect();
            graph.insert(dep.name.clone(), deps);
        }

        // Check for cycles using DFS
        let mut visited = HashSet::new();
        let mut rec_stack = HashSet::new();

        for package in graph.keys() {
            if !visited.contains(package) {
                if Self::has_cycle(&graph, package, &mut visited, &mut rec_stack) {
                    return Err(NxError::ResolutionFailed(format!(
                        "Circular dependency detected involving package '{}'",
                        package
                    )));
                }
            }
        }

        Ok(())
    }

    /// Check for cycles in dependency graph using DFS
    fn has_cycle(
        graph: &HashMap<String, Vec<String>>,
        node: &str,
        visited: &mut HashSet<String>,
        rec_stack: &mut HashSet<String>,
    ) -> bool {
        visited.insert(node.to_string());
        rec_stack.insert(node.to_string());

        if let Some(neighbors) = graph.get(node) {
            for neighbor in neighbors {
                if !visited.contains(neighbor) {
                    if Self::has_cycle(graph, neighbor, visited, rec_stack) {
                        return true;
                    }
                } else if rec_stack.contains(neighbor) {
                    return true;
                }
            }
        }

        rec_stack.remove(node);
        false
    }

    /// Get dependency tree as a flat list (topologically sorted)
    pub fn topological_sort(dependencies: &[ResolvedDependency]) -> Result<Vec<String>> {
        let mut graph: HashMap<String, Vec<String>> = HashMap::new();
        let mut in_degree: HashMap<String, usize> = HashMap::new();

        // Initialize
        for dep in dependencies {
            graph.insert(dep.name.clone(), Vec::new());
            in_degree.insert(dep.name.clone(), 0);
        }

        // Build graph and calculate in-degrees
        for dep in dependencies {
            for child in dep.dependencies.keys() {
                if graph.contains_key(child) {
                    graph.get_mut(&dep.name).unwrap().push(child.clone());
                    *in_degree.get_mut(child).unwrap() += 1;
                }
            }
        }

        // Kahn's algorithm
        let mut queue: VecDeque<String> = in_degree
            .iter()
            .filter(|(_, &degree)| degree == 0)
            .map(|(name, _)| name.clone())
            .collect();

        let mut result = Vec::new();

        while let Some(node) = queue.pop_front() {
            result.push(node.clone());

            if let Some(neighbors) = graph.get(&node) {
                for neighbor in neighbors {
                    let degree = in_degree.get_mut(neighbor).unwrap();
                    *degree -= 1;
                    if *degree == 0 {
                        queue.push_back(neighbor.clone());
                    }
                }
            }
        }

        if result.len() != dependencies.len() {
            return Err(NxError::ResolutionFailed(
                "Circular dependency detected during topological sort".to_string(),
            ));
        }

        Ok(result)
    }
}
