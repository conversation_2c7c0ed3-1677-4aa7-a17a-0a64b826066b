[package]
name = "nx"
version = "0.1.0"
edition = "2021"
description = "Ultra-fast npm package manager written in Rust"
license = "MIT"
authors = ["nx-team"]
repository = "https://github.com/nx-team/nx"
keywords = ["npm", "package-manager", "rust", "cli", "fast"]
categories = ["command-line-utilities", "development-tools"]

[[bin]]
name = "nx"
path = "src/main.rs"

[dependencies]
# CLI framework
clap = { version = "4.4", features = ["derive", "color"] }

# Async runtime and HTTP client
tokio = { version = "1.35", features = ["full"] }
reqwest = { version = "0.11", features = ["json", "stream"] }
futures-util = "0.3"
futures = "0.3"
rayon = "1.8"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"

# Semver handling
semver = { version = "1.0", features = ["serde"] }

# Compression and archives
flate2 = "1.0"
tar = "0.4"
zstd = "0.13"

# Hashing and integrity
sha2 = "0.10"
base64 = "0.21"

# File system operations
fs_extra = "1.3"
walkdir = "2.4"
memmap2 = "0.9"

# Performance optimizations
bloom = "0.3"

# Logging and tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Terminal UI and progress
console = "0.15"
indicatif = "0.17"
crossterm = "0.27"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Utilities
uuid = { version = "1.6", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
dirs = "5.0"
url = "2.5"

[dev-dependencies]
tempfile = "3.8"
assert_cmd = "2.0"
predicates = "3.0"
criterion = { version = "0.5", features = ["html_reports"] }
fastrand = "2.0"

[build-dependencies]
chrono = { version = "0.4", features = ["serde"] }

[profile.release]
opt-level = 3
lto = "fat"
codegen-units = 1
panic = "abort"
strip = true
debug = false
overflow-checks = false
incremental = false
rpath = false

[[bench]]
name = "benchmark"
harness = false
