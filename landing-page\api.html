<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Reference - nx Package Manager</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .code-block {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
        }
        .endpoint-card {
            transition: all 0.3s ease;
        }
        .endpoint-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-white text-gray-900">
    <!-- Navigation -->
    <nav class="fixed top-0 w-full bg-white/90 backdrop-blur-md border-b border-gray-100 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-sm">nx</span>
                    </div>
                    <span class="text-xl font-bold">nx API Reference</span>
                </div>
                <div class="flex space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-purple-600 transition">Home</a>
                    <a href="docs.html" class="text-gray-700 hover:text-purple-600 transition">Docs</a>
                    <a href="api.html" class="text-purple-600 font-medium">API</a>
                    <a href="examples.html" class="text-gray-700 hover:text-purple-600 transition">Examples</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="pt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="text-center mb-12">
                <h1 class="text-4xl font-bold mb-4">API Reference</h1>
                <p class="text-xl text-gray-600">Complete API documentation for nx package manager</p>
            </div>

            <!-- CLI Commands API -->
            <section class="mb-12">
                <h2 class="text-3xl font-semibold mb-8">CLI Commands</h2>
                
                <div class="grid gap-6">
                    <!-- Install Command -->
                    <div class="endpoint-card bg-white border border-gray-200 rounded-lg p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-xl font-semibold">nx install</h3>
                            <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">POST</span>
                        </div>
                        <p class="text-gray-600 mb-4">Install packages from package.json or specific packages</p>
                        
                        <div class="space-y-4">
                            <div>
                                <h4 class="font-semibold mb-2">Usage</h4>
                                <div class="code-block p-3 rounded text-sm">
                                    <code>nx install [packages...] [options]</code>
                                </div>
                            </div>
                            
                            <div>
                                <h4 class="font-semibold mb-2">Options</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <code class="bg-gray-100 px-2 py-1 rounded">-D, --save-dev</code>
                                        <p class="text-gray-600 mt-1">Save to devDependencies</p>
                                    </div>
                                    <div>
                                        <code class="bg-gray-100 px-2 py-1 rounded">-E, --save-exact</code>
                                        <p class="text-gray-600 mt-1">Save exact version</p>
                                    </div>
                                    <div>
                                        <code class="bg-gray-100 px-2 py-1 rounded">--force</code>
                                        <p class="text-gray-600 mt-1">Force reinstall</p>
                                    </div>
                                    <div>
                                        <code class="bg-gray-100 px-2 py-1 rounded">--registry</code>
                                        <p class="text-gray-600 mt-1">Custom registry URL</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Init Command -->
                    <div class="endpoint-card bg-white border border-gray-200 rounded-lg p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-xl font-semibold">nx init</h3>
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">CREATE</span>
                        </div>
                        <p class="text-gray-600 mb-4">Initialize a new JavaScript or TypeScript project</p>
                        
                        <div class="space-y-4">
                            <div>
                                <h4 class="font-semibold mb-2">Usage</h4>
                                <div class="code-block p-3 rounded text-sm">
                                    <code>nx init [name] [options]</code>
                                </div>
                            </div>
                            
                            <div>
                                <h4 class="font-semibold mb-2">Options</h4>
                                <div class="space-y-2 text-sm">
                                    <div>
                                        <code class="bg-gray-100 px-2 py-1 rounded">--typescript</code>
                                        <p class="text-gray-600 mt-1">Create TypeScript project</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Run Command -->
                    <div class="endpoint-card bg-white border border-gray-200 rounded-lg p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-xl font-semibold">nx run</h3>
                            <span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded">EXECUTE</span>
                        </div>
                        <p class="text-gray-600 mb-4">Run scripts defined in package.json</p>
                        
                        <div class="space-y-4">
                            <div>
                                <h4 class="font-semibold mb-2">Usage</h4>
                                <div class="code-block p-3 rounded text-sm">
                                    <code>nx run [script] [options]</code>
                                </div>
                            </div>
                            
                            <div>
                                <h4 class="font-semibold mb-2">Examples</h4>
                                <div class="space-y-2">
                                    <div class="code-block p-2 rounded text-sm">
                                        <code>nx run start</code>
                                    </div>
                                    <div class="code-block p-2 rounded text-sm">
                                        <code>nx run build</code>
                                    </div>
                                    <div class="code-block p-2 rounded text-sm">
                                        <code>nx run test</code>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Cache Commands -->
                    <div class="endpoint-card bg-white border border-gray-200 rounded-lg p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-xl font-semibold">nx cache</h3>
                            <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded">MANAGE</span>
                        </div>
                        <p class="text-gray-600 mb-4">Manage the global package cache</p>
                        
                        <div class="space-y-4">
                            <div>
                                <h4 class="font-semibold mb-2">Subcommands</h4>
                                <div class="space-y-2">
                                    <div class="code-block p-2 rounded text-sm">
                                        <code>nx cache info</code>
                                        <p class="text-gray-600 text-xs mt-1">Show cache information</p>
                                    </div>
                                    <div class="code-block p-2 rounded text-sm">
                                        <code>nx cache clear [package]</code>
                                        <p class="text-gray-600 text-xs mt-1">Clear cache for specific package or all</p>
                                    </div>
                                    <div class="code-block p-2 rounded text-sm">
                                        <code>nx cache verify</code>
                                        <p class="text-gray-600 text-xs mt-1">Verify cache integrity</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Configuration API -->
            <section class="mb-12">
                <h2 class="text-3xl font-semibold mb-8">Configuration Options</h2>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Option</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Default</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">registry</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">string</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">https://registry.npmjs.org</td>
                                <td class="px-6 py-4 text-sm text-gray-500">npm registry URL</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">cache_dir</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">string</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">~/.nx</td>
                                <td class="px-6 py-4 text-sm text-gray-500">Cache directory path</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">concurrency</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">number</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">8</td>
                                <td class="px-6 py-4 text-sm text-gray-500">Concurrent downloads</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">timeout</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">number</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">30</td>
                                <td class="px-6 py-4 text-sm text-gray-500">Network timeout (seconds)</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">strict_ssl</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">boolean</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">true</td>
                                <td class="px-6 py-4 text-sm text-gray-500">Verify SSL certificates</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Package.json Schema -->
            <section class="mb-12">
                <h2 class="text-3xl font-semibold mb-8">package.json Schema</h2>
                
                <div class="bg-gray-50 rounded-lg p-6">
                    <pre class="text-sm overflow-x-auto"><code>{
  "name": "my-app",
  "version": "1.0.0",
  "description": "My awesome app",
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "build": "webpack",
    "test": "jest"
  },
  "dependencies": {
    "express": "^4.18.0",
    "lodash": "^4.17.21"
  },
  "devDependencies": {
    "webpack": "^5.0.0",
    "jest": "^29.0.0"
  },
  "peerDependencies": {
    "react": ">=16.8.0"
  },
  "optionalDependencies": {
    "fsevents": "^2.3.0"
  },
  "bin": {
    "my-cli": "./bin/cli.js"
  },
  "engines": {
    "node": ">=14.0.0"
  }
}</code></pre>
                </div>
            </section>

            <!-- Exit Codes -->
            <section class="mb-12">
                <h2 class="text-3xl font-semibold mb-8">Exit Codes</h2>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">0</td>
                                <td class="px-6 py-4 text-sm text-gray-500">Success</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">1</td>
                                <td class="px-6 py-4 text-sm text-gray-500">General error</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">2</td>
                                <td class="px-6 py-4 text-sm text-gray-500">Misuse of shell commands</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">3</td>
                                <td class="px-6 py-4 text-sm text-gray-500">Package not found</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">4</td>
                                <td class="px-6 py-4 text-sm text-gray-500">Network error</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Environment Variables -->
            <section class="mb-12">
                <h2 class="text-3xl font-semibold mb-8">Environment Variables</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-white border border-gray-200 rounded-lg p-4">
                        <h4 class="font-semibold mb-2">NX_REGISTRY</h4>
                        <p class="text-sm text-gray-600">Custom npm registry URL</p>
                        <div class="code-block p-2 rounded mt-2 text-sm">
                            <code>NX_REGISTRY=https://custom.registry.com</code>
                        </div>
                    </div>
                    
                    <div class="bg-white border border-gray-200 rounded-lg p-4">
                        <h4 class="font-semibold mb-2">NX_CACHE_DIR</h4>
                        <p class="text-sm text-gray-600">Custom cache directory</p>
                        <div class="code-block p-2 rounded mt-2 text-sm">
                            <code>NX_CACHE_DIR=/tmp/nx-cache</code>
                        </div>
                    </div>
                    
                    <div class="bg-white border border-gray-200 rounded-lg p-4">
                        <h4 class="font-semibold mb-2">NX_CONCURRENCY</h4>
                        <p class="text-sm text-gray-600">Number of concurrent downloads</p>
                        <div class="code-block p-2 rounded mt-2 text-sm">
                            <code>NX_CONCURRENCY=16</code>
                        </div>
                    </div>
                    
                    <div class="bg-white border border-gray-200 rounded-lg p-4">
                        <h4 class="font-semibold mb-2">NX_TIMEOUT</h4>
                        <p class="text-sm text-gray-600">Network timeout in seconds</p>
                        <div class="code-block p-2 rounded mt-2 text-sm">
                            <code>NX_TIMEOUT=60</code>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <p>&copy; 2024 nx team. API documentation for the ultra-fast package manager.</p>
            </div>
        </div>
    </footer>
</body>
</html>
