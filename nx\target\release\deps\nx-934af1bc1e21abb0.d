D:\Sandeep\_nx\nx\target\release\deps\nx-934af1bc1e21abb0.d: src\lib.rs src\cli.rs src\cache.rs src\config.rs src\download.rs src\error.rs src\install.rs src\lockfile.rs src\manifest.rs src\registry.rs src\resolver.rs src\ui.rs src\utils.rs

D:\Sandeep\_nx\nx\target\release\deps\libnx-934af1bc1e21abb0.rlib: src\lib.rs src\cli.rs src\cache.rs src\config.rs src\download.rs src\error.rs src\install.rs src\lockfile.rs src\manifest.rs src\registry.rs src\resolver.rs src\ui.rs src\utils.rs

D:\Sandeep\_nx\nx\target\release\deps\libnx-934af1bc1e21abb0.rmeta: src\lib.rs src\cli.rs src\cache.rs src\config.rs src\download.rs src\error.rs src\install.rs src\lockfile.rs src\manifest.rs src\registry.rs src\resolver.rs src\ui.rs src\utils.rs

src\lib.rs:
src\cli.rs:
src\cache.rs:
src\config.rs:
src\download.rs:
src\error.rs:
src\install.rs:
src\lockfile.rs:
src\manifest.rs:
src\registry.rs:
src\resolver.rs:
src\ui.rs:
src\utils.rs:

# env-dep:CARGO_PKG_VERSION=0.1.0
