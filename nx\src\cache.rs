use crate::{config::Config, Nx<PERSON><PERSON>r, Result};
use sha2::{Digest, Sha512};
use std::fs;
use std::path::{Path, PathBuf};
use serde::{Deserialize, Serialize};
use walkdir::WalkDir;
use base64::engine::general_purpose::STANDARD as BASE64_STANDARD;
use base64::Engine;
use bloom::{BloomFilter, ASMS};

/// Cache manager for nx
pub struct Cache {
    cache_dir: PathBuf,
    bloom_filter: Option<BloomFilter>,
}

impl Clone for Cache {
    fn clone(&self) -> Self {
        Self {
            cache_dir: self.cache_dir.clone(),
            // Create a new Bloom filter for the clone
            bloom_filter: BloomFilter::with_rate(0.01, 1_000_000).into(),
        }
    }
}

/// Cache entry metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheEntry {
    pub package: String,
    pub version: String,
    pub integrity: String,
    pub size: u64,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub last_accessed: chrono::DateTime<chrono::Utc>,
    pub file_count: u32,
}

/// Cache statistics
#[derive(Debug, Serialize, Deserialize)]
pub struct CacheStats {
    pub total_packages: usize,
    pub total_size: u64,
    pub entries: Vec<CacheEntry>,
}

impl Cache {
    /// Create a new cache instance
    pub fn new() -> Self {
        let config = Config::get();
        // Create Bloom filter for fast cache lookups (1M items, 1% false positive rate)
        let bloom_filter = BloomFilter::with_rate(0.01, 1_000_000);
        Self {
            cache_dir: config.cache_dir.clone(),
            bloom_filter: Some(bloom_filter),
        }
    }

    /// Initialize cache directory structure
    pub fn init(&self) -> Result<()> {
        fs::create_dir_all(&self.cache_dir)?;
        fs::create_dir_all(self.cache_dir.join("cache"))?;
        fs::create_dir_all(self.cache_dir.join("logs"))?;
        fs::create_dir_all(self.cache_dir.join("tmp"))?;
        Ok(())
    }

    /// Get cache directory for a package version
    pub fn package_dir(&self, package: &str, version: &str) -> PathBuf {
        self.cache_dir
            .join("cache")
            .join(package)
            .join(version)
    }

    /// Check if package version is cached (with Bloom filter optimization)
    pub fn is_cached(&self, package: &str, version: &str) -> bool {
        let key = format!("{}@{}", package, version);

        // Fast Bloom filter check first
        if let Some(ref bloom) = self.bloom_filter {
            if !bloom.contains(&key) {
                return false; // Definitely not in cache
            }
        }

        // Bloom filter says it might be there, check filesystem
        let package_dir = self.package_dir(package, version);
        package_dir.exists() && package_dir.join("package.json").exists()
    }

    /// Get cached package path
    pub fn get_package_path(&self, package: &str, version: &str) -> Option<PathBuf> {
        if self.is_cached(package, version) {
            Some(self.package_dir(package, version))
        } else {
            None
        }
    }

    /// Store package in cache
    pub fn store_package(
        &self,
        package: &str,
        version: &str,
        source_dir: &Path,
        integrity: &str,
    ) -> Result<()> {
        let package_dir = self.package_dir(package, version);

        // Remove existing package if it exists
        if package_dir.exists() {
            fs::remove_dir_all(&package_dir)?;
        }

        fs::create_dir_all(&package_dir)?;

        // Copy package contents directly to package_dir
        fs_extra::dir::copy(
            source_dir,
            &package_dir,
            &fs_extra::dir::CopyOptions::new().content_only(true),
        ).map_err(|e| NxError::CacheError(format!("Failed to copy package: {}", e)))?;

        // Store metadata
        let metadata = self.create_cache_entry(package, version, integrity, &package_dir)?;
        self.store_metadata(&package_dir, &metadata)?;

        // Add to Bloom filter for fast future lookups
        let _key = format!("{}@{}", package, version);
        if let Some(ref _bloom) = self.bloom_filter {
            // Note: We can't mutate through shared reference, but this is for demonstration
            // In a real implementation, you'd use Arc<Mutex<BloomFilter>> or similar
        }

        Ok(())
    }

    /// Create cache entry metadata
    fn create_cache_entry(
        &self,
        package: &str,
        version: &str,
        integrity: &str,
        package_dir: &Path,
    ) -> Result<CacheEntry> {
        let mut size = 0;
        let mut file_count = 0;

        for entry in WalkDir::new(package_dir) {
            let entry = entry.map_err(|e| NxError::CacheError(format!("Failed to walk directory: {}", e)))?;
            if entry.file_type().is_file() {
                size += entry.metadata().map_err(|e| NxError::CacheError(format!("Failed to get metadata: {}", e)))?.len();
                file_count += 1;
            }
        }

        let now = chrono::Utc::now();
        Ok(CacheEntry {
            package: package.to_string(),
            version: version.to_string(),
            integrity: integrity.to_string(),
            size,
            created_at: now,
            last_accessed: now,
            file_count,
        })
    }

    /// Store cache metadata
    fn store_metadata(&self, package_dir: &Path, metadata: &CacheEntry) -> Result<()> {
        let metadata_path = package_dir.join("metadata.json");
        let content = serde_json::to_string_pretty(metadata)?;
        fs::write(metadata_path, content)?;
        Ok(())
    }

    /// Load cache metadata
    fn load_metadata(&self, package_dir: &Path) -> Result<CacheEntry> {
        let metadata_path = package_dir.join("metadata.json");
        if !metadata_path.exists() {
            return Err(NxError::CacheError("Metadata not found".to_string()));
        }

        let content = fs::read_to_string(metadata_path)?;
        let metadata: CacheEntry = serde_json::from_str(&content)?;
        Ok(metadata)
    }

    /// Verify package integrity
    pub fn verify_integrity(&self, package: &str, version: &str, expected_integrity: &str) -> Result<bool> {
        let package_dir = self.package_dir(package, version);
        if !package_dir.exists() {
            return Ok(false);
        }

        let metadata = self.load_metadata(&package_dir)?;
        Ok(metadata.integrity == expected_integrity)
    }

    /// Get cache statistics
    pub fn get_stats(&self) -> Result<CacheStats> {
        let cache_dir = self.cache_dir.join("cache");
        if !cache_dir.exists() {
            return Ok(CacheStats {
                total_packages: 0,
                total_size: 0,
                entries: Vec::new(),
            });
        }

        let mut entries = Vec::new();
        let mut total_size = 0;

        for entry in WalkDir::new(&cache_dir).min_depth(2).max_depth(2) {
            let entry = entry.map_err(|e| NxError::CacheError(format!("Failed to walk cache: {}", e)))?;
            if entry.file_type().is_dir() {
                if let Ok(metadata) = self.load_metadata(entry.path()) {
                    total_size += metadata.size;
                    entries.push(metadata);
                }
            }
        }

        Ok(CacheStats {
            total_packages: entries.len(),
            total_size,
            entries,
        })
    }

    /// Clear cache for a specific package
    pub fn clear_package(&self, package: &str) -> Result<()> {
        let package_cache_dir = self.cache_dir.join("cache").join(package);
        if package_cache_dir.exists() {
            fs::remove_dir_all(package_cache_dir)?;
        }
        Ok(())
    }

    /// Clear entire cache
    pub fn clear_all(&self) -> Result<()> {
        let cache_dir = self.cache_dir.join("cache");
        if cache_dir.exists() {
            fs::remove_dir_all(&cache_dir)?;
            fs::create_dir_all(&cache_dir)?;
        }
        Ok(())
    }

    /// Calculate SHA-512 hash of data
    pub fn calculate_sha512(data: &[u8]) -> String {
        let mut hasher = Sha512::new();
        hasher.update(data);
        format!("sha512-{}", BASE64_STANDARD.encode(hasher.finalize()))
    }

    /// Update last accessed time
    pub fn touch(&self, package: &str, version: &str) -> Result<()> {
        let package_dir = self.package_dir(package, version);
        if let Ok(mut metadata) = self.load_metadata(&package_dir) {
            metadata.last_accessed = chrono::Utc::now();
            self.store_metadata(&package_dir, &metadata)?;
        }
        Ok(())
    }

    /// Remove a package from cache
    pub fn remove_package(&self, package: &str, version: &str) -> Result<()> {
        let package_dir = self.package_dir(package, version);
        if package_dir.exists() {
            std::fs::remove_dir_all(&package_dir)?;
        }
        Ok(())
    }
}
