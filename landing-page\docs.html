<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentation - nx Package Manager</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .code-block {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
        }
        .sidebar-link {
            transition: all 0.2s ease;
        }
        .sidebar-link:hover {
            background-color: #f3f4f6;
            color: #667eea;
        }
        .sidebar-link.active {
            background-color: #ede9fe;
            color: #667eea;
            border-right: 3px solid #667eea;
        }
    </style>
</head>
<body class="bg-white text-gray-900">
    <!-- Navigation -->
    <nav class="fixed top-0 w-full bg-white/90 backdrop-blur-md border-b border-gray-100 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-sm">nx</span>
                    </div>
                    <span class="text-xl font-bold">nx Documentation</span>
                </div>
                <div class="flex space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-purple-600 transition">Home</a>
                    <a href="docs.html" class="text-purple-600 font-medium">Docs</a>
                    <a href="api.html" class="text-gray-700 hover:text-purple-600 transition">API</a>
                    <a href="examples.html" class="text-gray-700 hover:text-purple-600 transition">Examples</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex pt-16">
        <!-- Sidebar -->
        <aside class="fixed left-0 top-16 h-full w-64 bg-gray-50 border-r border-gray-200 overflow-y-auto">
            <div class="p-4">
                <h3 class="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-4">Getting Started</h3>
                <nav class="space-y-1">
                    <a href="#installation" class="sidebar-link block px-3 py-2 rounded-md text-sm">Installation</a>
                    <a href="#quick-start" class="sidebar-link block px-3 py-2 rounded-md text-sm">Quick Start</a>
                    <a href="#configuration" class="sidebar-link block px-3 py-2 rounded-md text-sm">Configuration</a>
                </nav>

                <h3 class="text-sm font-semibold text-gray-500 uppercase tracking-wider mt-8 mb-4">Commands</h3>
                <nav class="space-y-1">
                    <a href="#install" class="sidebar-link block px-3 py-2 rounded-md text-sm">nx install</a>
                    <a href="#init" class="sidebar-link block px-3 py-2 rounded-md text-sm">nx init</a>
                    <a href="#run" class="sidebar-link block px-3 py-2 rounded-md text-sm">nx run</a>
                    <a href="#uninstall" class="sidebar-link block px-3 py-2 rounded-md text-sm">nx uninstall</a>
                    <a href="#update" class="sidebar-link block px-3 py-2 rounded-md text-sm">nx update</a>
                    <a href="#cache" class="sidebar-link block px-3 py-2 rounded-md text-sm">nx cache</a>
                    <a href="#config" class="sidebar-link block px-3 py-2 rounded-md text-sm">nx config</a>
                    <a href="#info" class="sidebar-link block px-3 py-2 rounded-md text-sm">nx info</a>
                    <a href="#list" class="sidebar-link block px-3 py-2 rounded-md text-sm">nx list</a>
                </nav>

                <h3 class="text-sm font-semibold text-gray-500 uppercase tracking-wider mt-8 mb-4">Advanced</h3>
                <nav class="space-y-1">
                    <a href="#workspaces" class="sidebar-link block px-3 py-2 rounded-md text-sm">Workspaces</a>
                    <a href="#lockfiles" class="sidebar-link block px-3 py-2 rounded-md text-sm">Lockfiles</a>
                    <a href="#cache-system" class="sidebar-link block px-3 py-2 rounded-md text-sm">Cache System</a>
                    <a href="#troubleshooting" class="sidebar-link block px-3 py-2 rounded-md text-sm">Troubleshooting</a>
                </nav>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="ml-64 flex-1 p-8">
            <div class="max-w-4xl">
                <h1 class="text-4xl font-bold mb-8">Documentation</h1>

                <!-- Installation -->
                <section id="installation" class="mb-12">
                    <h2 class="text-3xl font-semibold mb-6">Installation</h2>
                    
                    <div class="mb-6">
                        <h3 class="text-xl font-semibold mb-3">Via npm (Recommended)</h3>
                        <div class="code-block p-4 rounded-lg mb-4">
                            <code>npm install -g @nx-pm/nx</code>
                        </div>
                        <p class="text-gray-600">This installs nx globally on your system, making it available as a command-line tool.</p>
                    </div>

                    <div class="mb-6">
                        <h3 class="text-xl font-semibold mb-3">From Source</h3>
                        <div class="code-block p-4 rounded-lg mb-4">
                            <code>git clone https://github.com/nx-team/nx.git<br>
cd nx<br>
cargo build --release</code>
                        </div>
                        <p class="text-gray-600">Build from source if you want the latest development version or need to contribute.</p>
                    </div>

                    <div class="bg-blue-50 border-l-4 border-blue-400 p-4 rounded">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-info-circle text-blue-400"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-blue-700">
                                    After installation, run <code class="bg-blue-100 px-1 rounded">nx --version</code> to verify the installation.
                                </p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Quick Start -->
                <section id="quick-start" class="mb-12">
                    <h2 class="text-3xl font-semibold mb-6">Quick Start</h2>
                    
                    <div class="space-y-4">
                        <div>
                            <h4 class="font-semibold mb-2">1. Create a new project</h4>
                            <div class="code-block p-3 rounded">
                                <code>nx init my-app</code>
                            </div>
                        </div>
                        <div>
                            <h4 class="font-semibold mb-2">2. Navigate to project</h4>
                            <div class="code-block p-3 rounded">
                                <code>cd my-app</code>
                            </div>
                        </div>
                        <div>
                            <h4 class="font-semibold mb-2">3. Install dependencies</h4>
                            <div class="code-block p-3 rounded">
                                <code>nx install</code>
                            </div>
                        </div>
                        <div>
                            <h4 class="font-semibold mb-2">4. Run scripts</h4>
                            <div class="code-block p-3 rounded">
                                <code>nx run start</code>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Configuration -->
                <section id="configuration" class="mb-12">
                    <h2 class="text-3xl font-semibold mb-6">Configuration</h2>
                    
                    <p class="text-gray-600 mb-4">nx can be configured in multiple ways:</p>
                    
                    <div class="space-y-6">
                        <div>
                            <h4 class="font-semibold mb-2">Global config file</h4>
                            <div class="code-block p-3 rounded mb-2">
                                <code>~/.nx/config.json</code>
                            </div>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold mb-2">Environment variables</h4>
                            <div class="code-block p-3 rounded mb-2">
                                <code>NX_REGISTRY=https://registry.npmjs.org<br>
NX_CACHE_DIR=~/.nx<br>
NX_CONCURRENCY=8</code>
                            </div>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold mb-2">CLI flags</h4>
                            <div class="code-block p-3 rounded mb-2">
                                <code>nx install --registry https://custom.registry.com --concurrency 16</code>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6">
                        <h4 class="font-semibold mb-3">Configuration Options</h4>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Option</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Default</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">registry</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">https://registry.npmjs.org</td>
                                        <td class="px-6 py-4 text-sm text-gray-500">npm registry URL</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">cache_dir</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">~/.nx</td>
                                        <td class="px-6 py-4 text-sm text-gray-500">Cache directory path</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">concurrency</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">8</td>
                                        <td class="px-6 py-4 text-sm text-gray-500">Concurrent downloads</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">timeout</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">30</td>
                                        <td class="px-6 py-4 text-sm text-gray-500">Network timeout (seconds)</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">strict_ssl</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">true</td>
                                        <td class="px-6 py-4 text-sm text-gray-500">Verify SSL certificates</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>

                <!-- Commands Documentation -->
                <section id="install" class="mb-12">
                    <h2 class="text-3xl font-semibold mb-6">nx install</h2>
                    <p class="text-gray-600 mb-4">Install packages from package.json or specific packages.</p>
                    
                    <div class="space-y-4">
                        <div>
                            <h4 class="font-semibold mb-2">Install all dependencies</h4>
                            <div class="code-block p-3 rounded">
                                <code>nx install</code>
                            </div>
                        </div>
                        <div>
                            <h4 class="font-semibold mb-2">Install specific packages</h4>
                            <div class="code-block p-3 rounded">
                                <code>nx install lodash express</code>
                            </div>
                        </div>
                        <div>
                            <h4 class="font-semibold mb-2">Install dev dependencies</h4>
                            <div class="code-block p-3 rounded">
                                <code>nx install -D typescript @types/node</code>
                            </div>
                        </div>
                        <div>
                            <h4 class="font-semibold mb-2">Install exact versions</h4>
                            <div class="code-block p-3 rounded">
                                <code>nx install -E react@18.2.0</code>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6">
                        <h4 class="font-semibold mb-3">Options</h4>
                        <ul class="list-disc list-inside space-y-2 text-gray-600">
                            <li><code>-D, --save-dev</code> - Save to devDependencies</li>
                            <li><code>-E, --save-exact</code> - Save exact version</li>
                            <li><code>--force</code> - Force reinstall</li>
                            <li><code>--registry</code> - Custom registry URL</li>
                            <li><code>--concurrency</code> - Number of concurrent downloads</li>
                        </ul>
                    </div>
                </section>

                <!-- Continue with other commands... -->
                <section id="init" class="mb-12">
                    <h2 class="text-3xl font-semibold mb-6">nx init</h2>
                    <p class="text-gray-600 mb-4">Initialize a new JavaScript or TypeScript project.</p>
                    
                    <div class="space-y-4">
                        <div>
                            <h4 class="font-semibold mb-2">Create JavaScript project</h4>
                            <div class="code-block p-3 rounded">
                                <code>nx init my-app</code>
                            </div>
                        </div>
                        <div>
                            <h4 class="font-semibold mb-2">Create TypeScript project</h4>
                            <div class="code-block p-3 rounded">
                                <code>nx init my-app --typescript</code>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Add more sections as needed... -->
            </div>
        </main>
    </div>

    <script>
        // Smooth scrolling for sidebar links
        document.querySelectorAll('.sidebar-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                    
                    // Update active state
                    document.querySelectorAll('.sidebar-link').forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                }
            });
        });

        // Update active link on scroll
        window.addEventListener('scroll', () => {
            const sections = document.querySelectorAll('section[id]');
            const scrollPos = window.scrollY + 100;
            
            sections.forEach(section => {
                const top = section.offsetTop;
                const bottom = top + section.offsetHeight;
                const id = section.getAttribute('id');
                const link = document.querySelector(`a[href="#${id}"]`);
                
                if (scrollPos >= top && scrollPos <= bottom) {
                    document.querySelectorAll('.sidebar-link').forEach(l => l.classList.remove('active'));
                    if (link) link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
