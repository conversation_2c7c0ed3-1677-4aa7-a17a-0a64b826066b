{"name": "audit-test", "version": "1.0.0", "description": "A new package", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js"}, "dependencies": {"safe-buffer": "^5.2.1", "proxy-addr": "^2.0.7", "type-is": "^2.0.1", "side-channel": "^1.1.0", "escape-html": "^1.0.3", "es-errors": "^1.3.0", "side-channel-weakmap": "^1.0.2", "toidentifier": "^1.0.1", "iconv-lite": "^0.6.3", "negotiator": "^1.0.0", "gopd": "^1.2.0", "accepts": "^2.0.0", "bytes": "^3.1.2", "es-define-property": "^1.0.1", "serve-static": "^2.2.0", "lodash": "^4.17.21", "media-typer": "^1.1.0", "has-symbols": "^1.1.0", "merge-descriptors": "^2.0.0", "ms": "^2.1.3", "parseurl": "^1.3.3", "ipaddr.js": "^1.9.1", "unpipe": "^1.0.0", "on-finished": "^2.4.1", "get-proto": "^1.0.1", "raw-body": "^3.0.0", "cookie": "^0.7.2", "content-type": "^1.0.5", "ee-first": "^1.1.1", "object-inspect": "^1.13.4", "once": "^1.4.0", "forwarded": "^0.2.0", "get-intrinsic": "^1.3.0", "cookie-signature": "^1.2.2", "statuses": "^2.0.2", "side-channel-map": "^1.0.1", "inherits": "^2.0.4", "hasown": "^2.0.2", "router": "^2.2.0", "setprototypeof": "^1.2.0", "http-errors": "^2.0.0", "function-bind": "^1.1.2", "body-parser": "^2.2.0", "path-to-regexp": "^8.2.0", "is-promise": "^4.0.0", "dunder-proto": "^1.0.1", "depd": "^2.0.0", "content-disposition": "^1.0.0", "safer-buffer": "^2.1.2", "fresh": "^2.0.0", "call-bound": "^1.0.4", "mime-types": "^3.0.1", "es-object-atoms": "^1.1.1", "send": "^1.2.0", "vary": "^1.1.2", "debug": "^4.4.1", "encodeurl": "^2.0.0", "range-parser": "^1.2.1", "mime-db": "^1.54.0", "math-intrinsics": "^1.1.0", "finalhandler": "^2.1.0", "qs": "^6.14.0", "call-bind-apply-helpers": "^1.0.2", "etag": "^1.8.1", "side-channel-list": "^1.0.0", "wrappy": "^1.0.2", "express": "^5.1.0"}, "devDependencies": null, "peerDependencies": null, "optionalDependencies": null, "engines": null, "os": null, "cpu": null, "keywords": null, "author": null, "license": "MIT", "repository": null, "bugs": null, "homepage": null, "private": null, "publishConfig": null, "funding": null, "bin": null, "files": null, "workspaces": null}